import { getSupabaseClient } from "./db";
import { getLocalizedContent } from "@/lib/i18n-content";

export interface AIModel {
  id?: number;
  model_id: string;
  model_name: string;
  model_type: string;
  provider: string;
  api_endpoint: string;
  credits_per_unit: number;
  unit_type: string;
  is_active: boolean;
  description?: string; // 保留兼容性
  description_i18n?: Record<string, string>; // 新的多语言描述字段
  model_name_i18n?: Record<string, string>; // 新的多语言名称字段
  max_input_size?: number;
  supported_features?: string[];
  icon?: string;
  created_at?: string;
  updated_at?: string;
}

export interface AIModelUsage {
  id?: number;
  user_uuid: string;
  model_id: string;
  task_id: string;
  external_request_id?: string;
  input_size?: number;
  output_size?: number;
  credits_consumed: number;
  status: string;
  error_reason?: string;
  error_detail?: string;
  request_params?: any;
  response_data?: any;
  started_at?: string;
  completed_at?: string;
  created_at?: string;
}

/**
 * 处理模型数据，添加本地化内容
 */
export function processModelWithLocalization(model: any, locale: string = 'en'): AIModel {
  return {
    ...model,

    // 使用多语言字段，如果不存在则回退到原字段
    model_name: getLocalizedContent(model.model_name_i18n || model.model_name, locale),
    description: getLocalizedContent(model.description_i18n || model.description, locale),
  };
}

/**
 * 获取所有活跃的AI模型
 */
export async function getActiveAIModels(locale: string = 'en'): Promise<AIModel[]> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("ai_models")
    .select("*")
    .eq("is_active", true)
    .order("model_type", { ascending: true })
    .order("credits_per_unit", { ascending: true });

  if (error) {
    throw error;
  }

  // 处理多语言内容
  return (data || []).map(model => processModelWithLocalization(model, locale));
}

/**
 * 根据模型类型获取模型列表
 */
export async function getAIModelsByType(type: string, locale: string = 'en'): Promise<AIModel[]> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("ai_models")
    .select("*")
    .eq("model_type", type)
    .eq("is_active", true)
    .order("credits_per_unit", { ascending: true });

  if (error) {
    throw error;
  }

  // 处理多语言内容
  return (data || []).map(model => processModelWithLocalization(model, locale));
}

/**
 * 根据模型ID获取模型配置
 */
export async function getActiveAIModelById(modelId: string, locale: string = 'en'): Promise<AIModel | null> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("ai_models")
    .select("*")
    .eq("model_id", modelId)
    .eq("is_active", true)
    .single();

  if (error) {
    return null;
  }

  // 处理多语言内容
  return data ? processModelWithLocalization(data, locale) : null;
}

/**
 * 根据模型ID获取模型配置（包括不活跃的模型）
 */
export async function getActiveAIModelByIdIncludeInactive(modelId: string, locale: string = 'en'): Promise<AIModel | null> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("ai_models")
    .select("*")
    .eq("model_id", modelId)
    .single();

  if (error) {
    return null;
  }

  // 处理多语言内容
  return data ? processModelWithLocalization(data, locale) : null;
}

/**
 * 根据提供商获取模型列表
 */
export async function getAIModelsByProvider(provider: string, locale: string = 'en'): Promise<AIModel[]> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("ai_models")
    .select("*")
    .eq("provider", provider)
    .eq("is_active", true)
    .order("model_type", { ascending: true })
    .order("credits_per_unit", { ascending: true });

  if (error) {
    throw error;
  }

  // 处理多语言内容
  return (data || []).map(model => processModelWithLocalization(model, locale));
}

/**
 * 计算模型使用成本
 */
export async function calculateModelCost(
  modelId: string,
  inputSize: number,
  outputSize?: number
): Promise<number> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .rpc("calculate_model_cost", {
      p_model_id: modelId,
      p_input_size: inputSize,
      p_output_size: outputSize || 0
    });

  if (error) {
    throw error;
  }

  return data || 1;
}

/**
 * 创建模型使用记录
 */
export async function createAIModelUsage(usage: AIModelUsage): Promise<AIModelUsage> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("ai_model_usage")
    .insert(usage)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

/**
 * 更新模型使用记录
 */
export async function updateAIModelUsage(
  requestId: string,
  updates: Partial<AIModelUsage>
): Promise<AIModelUsage | null> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("ai_model_usage")
    .update(updates)
    .eq("request_id", requestId)
    .select()
    .single();

  if (error) {
    return null;
  }

  return data;
}

/**
 * 通过任务ID更新模型使用记录
 */
export async function updateAIModelUsageByTaskId(
  taskId: string,
  updates: Partial<AIModelUsage>
): Promise<AIModelUsage | null> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("ai_model_usage")
    .update(updates)
    .eq("task_id", taskId)
    .select()
    .single();

  if (error) {
    return null;
  }

  return data;
}

/**
 * 根据请求ID获取使用记录
 */
export async function getAIModelUsageByRequestId(requestId: string): Promise<AIModelUsage | null> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("ai_model_usage")
    .select("*")
    .eq("request_id", requestId)
    .single();

  if (error) {
    return null;
  }

  return data;
}

/**
 * 根据外部请求ID获取使用记录
 */
export async function getAIModelUsageByExternalRequestId(externalRequestId: string): Promise<AIModelUsage | null> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("ai_model_usage")
    .select("*")
    .eq("external_request_id", externalRequestId)
    .single();

  if (error) {
    return null;
  }

  return data;
}

/**
 * 根据任务ID获取使用记录
 */
export async function getAIModelUsageByTaskId(taskId: string): Promise<AIModelUsage | null> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("ai_model_usage")
    .select("*")
    .eq("task_id", taskId)
    .single();

  if (error) {
    return null;
  }

  return data;
}

/**
 * 获取用户的模型使用记录
 */
export async function getUserAIModelUsage(
  userUuid: string,
  page: number = 1,
  limit: number = 50
): Promise<AIModelUsage[]> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("ai_model_usage")
    .select("*")
    .eq("user_uuid", userUuid)
    .order("created_at", { ascending: false })
    .range((page - 1) * limit, page * limit - 1);

  if (error) {
    throw error;
  }

  return data || [];
}

/**
 * 获取用户积分使用统计
 */
export async function getUserCreditsUsageStats(userUuid: string) {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("user_credits_usage_stats")
    .select("*")
    .eq("user_uuid", userUuid)
    .order("total_credits_consumed", { ascending: false });

  if (error) {
    throw error;
  }

  return data || [];
}

/**
 * 获取模型使用统计
 */
export async function getModelUsageStats() {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("model_usage_stats")
    .select("*")
    .order("total_usage_count", { ascending: false });

  if (error) {
    throw error;
  }

  return data || [];
}

/**
 * 插入或更新AI模型配置
 */
export async function upsertAIModel(model: AIModel): Promise<AIModel> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("ai_models")
    .upsert(model, { onConflict: "model_id" })
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

/**
 * 禁用AI模型
 */
export async function disableAIModel(modelId: string): Promise<boolean> {
  const supabase = getSupabaseClient();
  const { error } = await supabase
    .from("ai_models")
    .update({ is_active: false })
    .eq("model_id", modelId);

  return !error;
}

/**
 * 启用AI模型
 */
export async function enableAIModel(modelId: string): Promise<boolean> {
  const supabase = getSupabaseClient();
  const { error } = await supabase
    .from("ai_models")
    .update({ is_active: true })
    .eq("model_id", modelId);

  return !error;
}
