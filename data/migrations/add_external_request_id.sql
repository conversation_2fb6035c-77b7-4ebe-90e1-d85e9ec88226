-- 添加task_id、external_request_id和provider字段到ai_model_usage表
-- 统一ID管理和多提供商支持

-- 添加字段
ALTER TABLE ai_model_usage ADD COLUMN IF NOT EXISTS task_id VARCHAR(255) UNIQUE;
ALTER TABLE ai_model_usage ADD COLUMN IF NOT EXISTS external_request_id VARCHAR(255);
ALTER TABLE ai_model_usage ADD COLUMN IF NOT EXISTS provider VARCHAR(50);

-- 添加索引
CREATE INDEX IF NOT EXISTS idx_ai_model_usage_task_id ON ai_model_usage(task_id);
CREATE INDEX IF NOT EXISTS idx_ai_model_usage_external_request ON ai_model_usage(external_request_id);
CREATE INDEX IF NOT EXISTS idx_ai_model_usage_provider ON ai_model_usage(provider);

-- 添加字段注释
COMMENT ON COLUMN ai_model_usage.task_id IS '统一的任务ID，用于前端查询';
COMMENT ON COLUMN ai_model_usage.external_request_id IS '外部提供商的任务ID (GRSAI/Replicate等)';
COMMENT ON COLUMN ai_model_usage.provider IS '提供商：grsai, replicate等';

-- 迁移现有数据：生成task_id和设置默认provider
UPDATE ai_model_usage
SET
  task_id = COALESCE(task_id, gen_random_uuid()::text),
  provider = COALESCE(provider, 'grsai'),
  external_request_id = COALESCE(
    external_request_id,
    CASE
      WHEN response_data->>'data' IS NOT NULL AND response_data->'data'->>'id' IS NOT NULL
      THEN response_data->'data'->>'id'
      WHEN response_data->>'id' IS NOT NULL
      THEN response_data->>'id'
      ELSE NULL
    END
  )
WHERE task_id IS NULL OR provider IS NULL OR external_request_id IS NULL;
