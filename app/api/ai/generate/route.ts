import { respData, respErr, respJson } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { GrsAIService } from "@/services/grsai-provider";
import { ReplicateAIService } from "@/services/replicate-ai-service";
import { checkSufficientCredits, decreaseCreditsForAIModel, refundCreditsForAIModel } from "@/services/credit";
import { getActiveAIModelById, getActiveAIModelByIdIncludeInactive, calculateModelCost } from "@/models/ai-model";
import { AIRequest } from "@/types/ai-model";
import { convertAllParametersForAPI } from "@/lib/model-parameter-loader";



export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { model, type, prompt, options } = body as {
      model: string;
      type: string;
      prompt: string;
      options?: any
    };

    // 验证必需参数
    if (!model || !type || !prompt) {
      return respErr("Missing required parameters: model, type, prompt");
    }

    // 验证用户身份
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respJson(-2, "Authentication required");
    }

    // 验证模型是否存在（包括不活跃的模型，用于支持特殊 provider）
    let modelConfig = await getActiveAIModelById(model);
    if (!modelConfig) {
      // 如果活跃模型中找不到，尝试查找不活跃的模型
      modelConfig = await getActiveAIModelByIdIncludeInactive(model);
    }
    if (!modelConfig) {
      return respErr(`Model ${model} not found`);
    }

    // 转换参数为API格式
    const convertedOptions = convertAllParametersForAPI(model, options || {});
    console.log(`[API] Original options:`, options);
    console.log(`[API] Converted options:`, convertedOptions);

    // 构建AI请求
    const aiRequest: AIRequest = {
      model,
      type,
      prompt,
      options: {
        ...convertedOptions,
        // 确保图片上传数据正确传递
        uploadedImages: options?.uploadedImages || [],
        referenceImages: options?.referenceImages || [],
        firstFrameUrl: options?.firstFrameUrl
      }
    };

    console.log(`[API] Final AI request:`, aiRequest);

    // 估算积分成本
    let estimatedCost: number;
    try {
      switch (type) {
        case 'text':
          const inputTokens = estimateTokens(prompt);
          const maxTokens = options?.max_tokens || 1000;
          estimatedCost = await calculateModelCost(model, inputTokens, maxTokens);
          break;
        case 'image':
          const variants = options?.variants || 1;
          estimatedCost = modelConfig.credits_per_unit * variants;
          break;
        case 'video':
          estimatedCost = modelConfig.credits_per_unit;
          break;
        default:
          estimatedCost = modelConfig.credits_per_unit;
      }
    } catch (error) {
      return respErr("Failed to calculate cost");
    }

    // 检查积分余额
    const hasSufficientCredits = await checkSufficientCredits(user_uuid, estimatedCost);
    if (!hasSufficientCredits) {
      return respJson(-3, "Insufficient credits", {
        required_credits: estimatedCost,
        error_code: "INSUFFICIENT_CREDITS"
      });
    }

    // 根据模型的 provider 选择对应的服务
    let aiService: GrsAIService | ReplicateAIService;

    if (modelConfig.provider === 'replicate') {
      console.log(`[AI Generate] Using Replicate provider for model: ${model}`);
      aiService = new ReplicateAIService();
    } else {
      console.log(`[AI Generate] Using GRSAI provider for model: ${model}`);
      aiService = new GrsAIService();
    }

    try {
      // 处理AI请求
      const response = await aiService.processRequestFromClient(user_uuid, aiRequest);

      // 如果是流式响应，直接返回
      if (type === 'text' && options?.stream) {
        return new Response(JSON.stringify(response), {
          headers: {
            'Content-Type': 'application/json',
          },
        });
      }

      // 扣减积分（只对同步成功的任务立即扣减，异步任务在result API中扣减）
      if (response.status === 'success') {
        await decreaseCreditsForAIModel({
          user_uuid,
          model_id: model,
          request_id: response.task_id,
          credits: response.usage?.credits_consumed || estimatedCost
        });
      }

      return respData(response);
    } catch (error) {
      console.error("AI generation failed:", error);

      // 如果是API错误，可能需要退还积分
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      return respErr(`AI generation failed: ${errorMessage}`);
    }
  } catch (error) {
    console.error("Request processing failed:", error);
    return respErr("Request processing failed");
  }
}

/**
 * 估算token数量的辅助函数
 */
function estimateTokens(text: string): number {
  // 简单估算：中文按字符数，英文按单词数 * 1.3
  const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
  const englishWords = text.replace(/[\u4e00-\u9fff]/g, '').split(/\s+/).filter(w => w.length > 0).length;
  return Math.ceil(chineseChars + englishWords * 1.3);
}
